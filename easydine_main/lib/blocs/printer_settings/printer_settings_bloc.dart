// lib/blocs/printer_settings/printer_settings_bloc.dart

import 'dart:convert';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../models/printer_settings.dart';
import '../../services/pos_printer_service.dart';
import '../../services/menu_service.dart';

part 'printer_settings_event.dart';
part 'printer_settings_state.dart';

class PrinterSettingsBloc
    extends Bloc<PrinterSettingsEvent, PrinterSettingsState> {
  static const String _printerSettingsKey = 'printer_settings';
  final POSPrinterService _printerService = POSPrinterService();

  PrinterSettingsBloc() : super(PrinterSettingsState.initial()) {
    on<LoadPrinterSettings>(_onLoadPrinterSettings);
    on<UpdatePrinterAssignment>(_onUpdatePrinterAssignment);
    on<UpdateCategoryAssignment>(_onUpdateCategoryAssignment);
    on<AddPrinterDevice>(_onAddPrinterDevice);
    on<RemovePrinterDevice>(_onRemovePrinterDevice);
    on<UpdatePrinterDevice>(_onUpdatePrinterDevice);
    on<ResetPrinterSettings>(_onResetPrinterSettings);
    on<DiscoverPrinters>(_onDiscoverPrinters);
    on<ConnectToPrinter>(_onConnectToPrinter);
    on<DisconnectPrinter>(_onDisconnectPrinter);
    on<TestPrinter>(_onTestPrinter);
  }

  Future<void> _onLoadPrinterSettings(
    LoadPrinterSettings event,
    Emitter<PrinterSettingsState> emit,
  ) async {
    emit(state.copyWith(isLoading: true));

    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_printerSettingsKey);

      PrinterSettings settings;
      if (settingsJson != null) {
        final settingsMap = jsonDecode(settingsJson) as Map<String, dynamic>;
        settings = PrinterSettings.fromJson(settingsMap);
      } else {
        settings = _createDefaultSettings();
        await _savePrinterSettings(settings);
      }

      // Get available categories from menu service
      final availableCategories = MenuService.getAllCategories();

      emit(state.copyWith(
        isLoading: false,
        settings: settings,
        availableCategories: availableCategories,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: 'Failed to load printer settings: $e',
      ));
    }
  }

  Future<void> _onUpdatePrinterAssignment(
    UpdatePrinterAssignment event,
    Emitter<PrinterSettingsState> emit,
  ) async {
    try {
      final updatedAssignments =
          Map<String, String>.from(state.settings.printerAssignments);

      if (event.location.isEmpty || event.location == 'Not assigned') {
        updatedAssignments.remove(event.printerId);
      } else {
        updatedAssignments[event.printerId] = event.location;
      }

      final updatedSettings = state.settings.copyWith(
        printerAssignments: updatedAssignments,
      );

      await _savePrinterSettings(updatedSettings);
      emit(state.copyWith(settings: updatedSettings));
    } catch (e) {
      emit(state.copyWith(error: 'Failed to update printer assignment: $e'));
    }
  }

  Future<void> _onUpdateCategoryAssignment(
    UpdateCategoryAssignment event,
    Emitter<PrinterSettingsState> emit,
  ) async {
    try {
      final updatedCategoryAssignments =
          Map<String, List<String>>.from(state.settings.categoryAssignments);

      // Remove category from all locations first
      for (final location in updatedCategoryAssignments.keys) {
        updatedCategoryAssignments[location]?.remove(event.category);
      }

      // Add category to new location if specified
      if (event.location.isNotEmpty && event.location != 'Not assigned') {
        if (!updatedCategoryAssignments.containsKey(event.location)) {
          updatedCategoryAssignments[event.location] = [];
        }
        if (!updatedCategoryAssignments[event.location]!
            .contains(event.category)) {
          updatedCategoryAssignments[event.location]!.add(event.category);
        }
      }

      // Clean up empty location entries
      updatedCategoryAssignments.removeWhere((key, value) => value.isEmpty);

      final updatedSettings = state.settings.copyWith(
        categoryAssignments: updatedCategoryAssignments,
      );

      await _savePrinterSettings(updatedSettings);
      emit(state.copyWith(settings: updatedSettings));
    } catch (e) {
      emit(state.copyWith(error: 'Failed to update category assignment: $e'));
    }
  }

  Future<void> _onAddPrinterDevice(
    AddPrinterDevice event,
    Emitter<PrinterSettingsState> emit,
  ) async {
    try {
      final updatedPrinters =
          List<PrinterDevice>.from(state.settings.availablePrinters);
      updatedPrinters.add(event.printer);

      final updatedSettings = state.settings.copyWith(
        availablePrinters: updatedPrinters,
      );

      await _savePrinterSettings(updatedSettings);
      emit(state.copyWith(settings: updatedSettings));
    } catch (e) {
      emit(state.copyWith(error: 'Failed to add printer device: $e'));
    }
  }

  Future<void> _onRemovePrinterDevice(
    RemovePrinterDevice event,
    Emitter<PrinterSettingsState> emit,
  ) async {
    try {
      final updatedPrinters =
          List<PrinterDevice>.from(state.settings.availablePrinters);
      updatedPrinters.removeWhere((printer) => printer.id == event.printerId);

      // Also remove from assignments
      final updatedAssignments =
          Map<String, String>.from(state.settings.printerAssignments);
      updatedAssignments.remove(event.printerId);

      final updatedSettings = state.settings.copyWith(
        availablePrinters: updatedPrinters,
        printerAssignments: updatedAssignments,
      );

      await _savePrinterSettings(updatedSettings);
      emit(state.copyWith(settings: updatedSettings));
    } catch (e) {
      emit(state.copyWith(error: 'Failed to remove printer device: $e'));
    }
  }

  Future<void> _onUpdatePrinterDevice(
    UpdatePrinterDevice event,
    Emitter<PrinterSettingsState> emit,
  ) async {
    try {
      final updatedPrinters =
          List<PrinterDevice>.from(state.settings.availablePrinters);
      final index = updatedPrinters
          .indexWhere((printer) => printer.id == event.printer.id);

      if (index != -1) {
        updatedPrinters[index] = event.printer;
      }

      final updatedSettings = state.settings.copyWith(
        availablePrinters: updatedPrinters,
      );

      await _savePrinterSettings(updatedSettings);
      emit(state.copyWith(settings: updatedSettings));
    } catch (e) {
      emit(state.copyWith(error: 'Failed to update printer device: $e'));
    }
  }

  Future<void> _onResetPrinterSettings(
    ResetPrinterSettings event,
    Emitter<PrinterSettingsState> emit,
  ) async {
    try {
      final defaultSettings = _createDefaultSettings();
      await _savePrinterSettings(defaultSettings);
      emit(state.copyWith(settings: defaultSettings));
    } catch (e) {
      emit(state.copyWith(error: 'Failed to reset printer settings: $e'));
    }
  }

  Future<void> _onDiscoverPrinters(
    DiscoverPrinters event,
    Emitter<PrinterSettingsState> emit,
  ) async {
    emit(state.copyWith(isDiscovering: true));

    try {
      // Initialize printer service
      await _printerService.initialize();

      // Discover real printers using the POS printer service
      final discoveredPrinters = await _printerService.discoverPrinters();

      final updatedPrinters =
          List<PrinterDevice>.from(state.settings.availablePrinters);

      // Add newly discovered printers that aren't already in the list
      for (final printer in discoveredPrinters) {
        if (!updatedPrinters.any((p) => p.id == printer.id)) {
          updatedPrinters.add(printer);
        }
      }

      final updatedSettings = state.settings.copyWith(
        availablePrinters: updatedPrinters,
      );

      await _savePrinterSettings(updatedSettings);
      emit(state.copyWith(
        settings: updatedSettings,
        isDiscovering: false,
      ));
    } catch (e) {
      emit(state.copyWith(
        isDiscovering: false,
        error: 'Failed to discover printers: $e',
      ));
    }
  }

  PrinterSettings _createDefaultSettings() {
    // Create some default printers
    final defaultPrinters = [
      PrinterDevice(
        id: 'printer_1',
        name: 'Kitchen 1',
        type: 'thermal',
        isConnected: false,
      ),
      PrinterDevice(
        id: 'printer_2',
        name: 'Kitchen 2',
        type: 'thermal',
        isConnected: false,
      ),
      PrinterDevice(
        id: 'printer_3',
        name: 'Counter',
        type: 'receipt',
        isConnected: false,
      ),
      PrinterDevice(
        id: 'printer_4',
        name: 'Bar Station',
        type: 'thermal',
        isConnected: false,
      ),
    ];

    // Default assignments
    final defaultPrinterAssignments = {
      'printer_1': PrinterLocation.kitchen1,
      'printer_2': PrinterLocation.kitchen2,
      'printer_3': PrinterLocation.counter,
    };

    final defaultCategoryAssignments = {
      PrinterLocation.kitchen1: ['Food', 'Mains', 'Appetizers'],
      PrinterLocation.kitchen2: ['Food', 'Desserts'],
      PrinterLocation.counter: ['Drinks', 'Beverages'],
    };

    return PrinterSettings(
      printerAssignments: defaultPrinterAssignments,
      categoryAssignments: defaultCategoryAssignments,
      availablePrinters: defaultPrinters,
    );
  }

  Future<void> _onConnectToPrinter(
    ConnectToPrinter event,
    Emitter<PrinterSettingsState> emit,
  ) async {
    try {
      final printer = state.settings.availablePrinters
          .firstWhere((p) => p.id == event.printerId);

      final connected = await _printerService.connectToPrinter(printer);

      if (connected) {
        final updatedPrinter = printer.copyWith(isConnected: true);
        final updatedPrinters = state.settings.availablePrinters
            .map((p) => p.id == event.printerId ? updatedPrinter : p)
            .toList();

        final updatedSettings = state.settings.copyWith(
          availablePrinters: updatedPrinters,
        );

        await _savePrinterSettings(updatedSettings);
        emit(state.copyWith(settings: updatedSettings));
      } else {
        emit(state.copyWith(error: 'Failed to connect to printer'));
      }
    } catch (e) {
      emit(state.copyWith(error: 'Error connecting to printer: $e'));
    }
  }

  Future<void> _onDisconnectPrinter(
    DisconnectPrinter event,
    Emitter<PrinterSettingsState> emit,
  ) async {
    try {
      final printer = state.settings.availablePrinters
          .firstWhere((p) => p.id == event.printerId);

      final disconnected = await _printerService.disconnectPrinter(printer);

      if (disconnected) {
        final updatedPrinter = printer.copyWith(isConnected: false);
        final updatedPrinters = state.settings.availablePrinters
            .map((p) => p.id == event.printerId ? updatedPrinter : p)
            .toList();

        final updatedSettings = state.settings.copyWith(
          availablePrinters: updatedPrinters,
        );

        await _savePrinterSettings(updatedSettings);
        emit(state.copyWith(settings: updatedSettings));
      } else {
        emit(state.copyWith(error: 'Failed to disconnect printer'));
      }
    } catch (e) {
      emit(state.copyWith(error: 'Error disconnecting printer: $e'));
    }
  }

  Future<void> _onTestPrinter(
    TestPrinter event,
    Emitter<PrinterSettingsState> emit,
  ) async {
    try {
      final printer = state.settings.availablePrinters
          .firstWhere((p) => p.id == event.printerId);

      final testResult = await _printerService.printTestPage(printer);

      if (testResult) {
        emit(state.copyWith(error: null));
      } else {
        emit(state.copyWith(error: 'Test print failed'));
      }
    } catch (e) {
      emit(state.copyWith(error: 'Error testing printer: $e'));
    }
  }

  Future<void> _savePrinterSettings(PrinterSettings settings) async {
    final prefs = await SharedPreferences.getInstance();
    final settingsJson = jsonEncode(settings.toJson());
    await prefs.setString(_printerSettingsKey, settingsJson);
  }
}
