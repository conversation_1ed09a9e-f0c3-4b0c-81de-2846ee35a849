// lib/models/printer_settings.dart

class PrinterSettings {
  final Map<String, String> printerAssignments; // printer_id -> location_name
  final Map<String, List<String>>
      categoryAssignments; // location_name -> [categories]
  final List<PrinterDevice> availablePrinters;

  PrinterSettings({
    required this.printerAssignments,
    required this.categoryAssignments,
    required this.availablePrinters,
  });

  factory PrinterSettings.empty() {
    return PrinterSettings(
      printerAssignments: {},
      categoryAssignments: {},
      availablePrinters: [],
    );
  }

  factory PrinterSettings.fromJson(Map<String, dynamic> json) {
    return PrinterSettings(
      printerAssignments:
          Map<String, String>.from(json['printerAssignments'] ?? {}),
      categoryAssignments:
          (json['categoryAssignments'] as Map<String, dynamic>? ?? {})
              .map((key, value) => MapEntry(key, List<String>.from(value))),
      availablePrinters: (json['availablePrinters'] as List<dynamic>? ?? [])
          .map((printer) => PrinterDevice.fromJson(printer))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'printerAssignments': printerAssignments,
      'categoryAssignments': categoryAssignments,
      'availablePrinters':
          availablePrinters.map((printer) => printer.toJson()).toList(),
    };
  }

  PrinterSettings copyWith({
    Map<String, String>? printerAssignments,
    Map<String, List<String>>? categoryAssignments,
    List<PrinterDevice>? availablePrinters,
  }) {
    return PrinterSettings(
      printerAssignments: printerAssignments ?? this.printerAssignments,
      categoryAssignments: categoryAssignments ?? this.categoryAssignments,
      availablePrinters: availablePrinters ?? this.availablePrinters,
    );
  }

  // Get printer location for a specific category
  String? getPrinterLocationForCategory(String category) {
    for (final entry in categoryAssignments.entries) {
      if (entry.value.contains(category)) {
        return entry.key;
      }
    }
    return null;
  }

  // Get printer device for a specific category
  PrinterDevice? getPrinterForCategory(String category) {
    final location = getPrinterLocationForCategory(category);
    if (location == null) return null;

    final printerId = printerAssignments.entries
        .firstWhere((entry) => entry.value == location,
            orElse: () => const MapEntry('', ''))
        .key;

    if (printerId.isEmpty) return null;

    return availablePrinters.firstWhere(
      (printer) => printer.id == printerId,
      orElse: () => PrinterDevice.notAssigned(),
    );
  }

  // Get all assigned locations
  List<String> getAssignedLocations() {
    return printerAssignments.values.toSet().toList();
  }

  // Get categories assigned to a location
  List<String> getCategoriesForLocation(String location) {
    return categoryAssignments[location] ?? [];
  }
}

class PrinterDevice {
  final String id;
  final String name;
  final String
      type; // 'usb', 'bluetooth', 'network', 'thermal', 'receipt', etc.
  final bool isConnected;
  final String? ipAddress;
  final int? port;
  final String? deviceId;
  final String? macAddress;
  final int? vendorId;
  final int? productId;

  PrinterDevice({
    required this.id,
    required this.name,
    required this.type,
    required this.isConnected,
    this.ipAddress,
    this.port,
    this.deviceId,
    this.macAddress,
    this.vendorId,
    this.productId,
  });

  factory PrinterDevice.notAssigned() {
    return PrinterDevice(
      id: 'not_assigned',
      name: 'Not assigned',
      type: 'none',
      isConnected: false,
    );
  }

  factory PrinterDevice.fromJson(Map<String, dynamic> json) {
    return PrinterDevice(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      type: json['type'] ?? 'thermal',
      isConnected: json['isConnected'] ?? false,
      ipAddress: json['ipAddress'],
      port: json['port'],
      deviceId: json['deviceId'],
      macAddress: json['macAddress'],
      vendorId: json['vendorId'],
      productId: json['productId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'isConnected': isConnected,
      'ipAddress': ipAddress,
      'port': port,
      'deviceId': deviceId,
      'macAddress': macAddress,
      'vendorId': vendorId,
      'productId': productId,
    };
  }

  PrinterDevice copyWith({
    String? id,
    String? name,
    String? type,
    bool? isConnected,
    String? ipAddress,
    int? port,
    String? deviceId,
    String? macAddress,
    int? vendorId,
    int? productId,
  }) {
    return PrinterDevice(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      isConnected: isConnected ?? this.isConnected,
      ipAddress: ipAddress ?? this.ipAddress,
      port: port ?? this.port,
      deviceId: deviceId ?? this.deviceId,
      macAddress: macAddress ?? this.macAddress,
      vendorId: vendorId ?? this.vendorId,
      productId: productId ?? this.productId,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PrinterDevice && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// Predefined printer locations/stations
class PrinterLocation {
  static const String kitchen1 = 'Kitchen 1';
  static const String kitchen2 = 'Kitchen 2';
  static const String bar = 'Bar';
  static const String counter = 'Counter';
  static const String drinkStation = 'Drink Station';
  static const String dessertStation = 'Dessert Station';
  static const String grill = 'Grill Station';
  static const String coldPrep = 'Cold Prep';
  static const String hotPrep = 'Hot Prep';

  static List<String> getAllLocations() {
    return [
      kitchen1,
      kitchen2,
      bar,
      counter,
      drinkStation,
      dessertStation,
      grill,
      coldPrep,
      hotPrep,
    ];
  }
}

// Common food categories
class FoodCategory {
  static const String food = 'Food';
  static const String beverages = 'Beverages';
  static const String drinks = 'Drinks';
  static const String appetizers = 'Appetizers';
  static const String mains = 'Mains';
  static const String desserts = 'Desserts';
  static const String salads = 'Salads';
  static const String soups = 'Soups';
  static const String pizza = 'Pizza';
  static const String pasta = 'Pasta';
  static const String seafood = 'Seafood';
  static const String meat = 'Meat';
  static const String vegetarian = 'Vegetarian';
  static const String vegan = 'Vegan';
  static const String alcohol = 'Alcohol';
  static const String coffee = 'Coffee';
  static const String tea = 'Tea';
  static const String juice = 'Juice';
  static const String softDrinks = 'Soft Drinks';

  static List<String> getAllCategories() {
    return [
      food,
      beverages,
      drinks,
      appetizers,
      mains,
      desserts,
      salads,
      soups,
      pizza,
      pasta,
      seafood,
      meat,
      vegetarian,
      vegan,
      alcohol,
      coffee,
      tea,
      juice,
      softDrinks,
    ];
  }
}
