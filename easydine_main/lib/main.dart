import 'package:easydine_main/blocs/auth/auth_bloc.dart';
import 'package:easydine_main/blocs/cart/cart_bloc.dart';
import 'package:easydine_main/blocs/demopos/pos_bloc.dart';
import 'package:easydine_main/blocs/running_orders/running_orders_bloc.dart';
import 'package:easydine_main/blocs/running_orders/running_orders_event.dart';
import 'package:easydine_main/blocs/settings/settings_bloc.dart';
import 'package:easydine_main/blocs/table/table_bloc.dart';
import 'package:easydine_main/router/router_config.dart';
import 'package:easydine_main/services/navigation_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:sizer/sizer.dart';
import 'blocs/checklist/checklist_bloc.dart';
import 'blocs/pos/pos_bloc.dart';
import 'blocs/session/session_bloc.dart';
import 'blocs/reports/reports_bloc.dart';
import 'blocs/table/table_event.dart';
import 'blocs/staff/staff_bloc.dart';
import 'blocs/attendance/attendance_bloc.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await dotenv.load(fileName: "assets/.env");
  runApp(MultiBlocProvider(
    providers: [
      BlocProvider<AuthBloc>(
        create: (context) => AuthBloc(),
      ),
      BlocProvider<RunningOrdersBloc>(
        create: (context) => RunningOrdersBloc()..add(FetchRunningOrders()),
      ),
      BlocProvider(create: (context) => SettingsBloc()),
      BlocProvider(create: (context) => ChecklistBloc()),
      BlocProvider(create: (context) => CartBloc()),
      BlocProvider(create: (context) => SessionBloc()),
      BlocProvider(
          create: (context) => POSBloc(
                cartBloc: context.read<CartBloc>(),
                sessionBloc: context.read<SessionBloc>(),
              )),
      BlocProvider(create: (context) => DemoPOSBloc()),
      BlocProvider<TableBloc>(
        create: (context) => TableBloc(
          runningOrdersBloc: context.read<RunningOrdersBloc>(),
        )..add(LoadTables()),
      ),
      BlocProvider<ReportsBloc>(
        create: (context) => ReportsBloc(),
      ),
      BlocProvider<StaffBloc>(
        create: (context) => StaffBloc(),
      ),
      BlocProvider<AttendanceBloc>(
        create: (context) => AttendanceBloc(),
      ),
    ],
    child: const MyApp(),
  ));
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    // Enable system navigation while hiding status bar for immersive experience
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
        overlays: [SystemUiOverlay.bottom] // Keep navigation bar visible
        );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Sizer(
      builder: (context, orientation, deviceType) {
        return PopScope(
          canPop: false,
          onPopInvokedWithResult: (didPop, result) async {
            // Always prevent the default back button behavior
            if (!didPop) {
              // Handle back button through our custom navigation service
              await NavigationService.handleBackButton(context);
            }
          },
          child: MaterialApp.router(
            debugShowCheckedModeBanner: false,
            title: 'EasyDine Waiter AIO',
            routerConfig: router,
          ),
        );
      },
    );
  }
}
