import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';
import '../router/router_constants.dart';

class NavigationService {
  static const MethodChannel _channel =
      MethodChannel('com.zynktech.easydine_main/navigation');

  /// Handle back button press - navigate to home instead of closing app
  static Future<bool> handleBackButton(BuildContext context) async {
    try {
      final router = GoRouter.of(context);
      final currentLocation =
          router.routerDelegate.currentConfiguration.fullPath;

      print('🔙 Back button pressed on: $currentLocation');

      // Special handling for certain pages that should not allow back navigation
      final restrictedPages = ['/daily-checklist', '/pin-entry', '/login'];
      if (restrictedPages.contains(currentLocation)) {
        print('🚫 Back navigation restricted on: $currentLocation');
        return true; // Handled - prevent back navigation
      }

      // If we're already on home page, minimize the app instead of closing
      if (currentLocation == RouterConstants.home ||
          currentLocation == '/home') {
        print('🏠 On home page, minimizing app');
        await minimizeApp();
        return true; // Handled - don't close the app
      }

      // For other pages, navigate to home
      print('🏠 Navigating to home page');
      router.go(RouterConstants.home);
      return true; // Handled - don't close the app
    } catch (e) {
      print('❌ Error handling back button: $e');
      // Even on error, try to minimize app to prevent closing
      try {
        await minimizeApp();
      } catch (minimizeError) {
        print('❌ Error minimizing app: $minimizeError');
      }
      return true; // Always prevent app from closing
    }
  }

  /// Minimize the app instead of closing it
  static Future<void> minimizeApp() async {
    try {
      await _channel.invokeMethod('minimizeApp');
    } on PlatformException catch (e) {
      debugPrint("Failed to minimize app: '${e.message}'.");
    }
  }
}
