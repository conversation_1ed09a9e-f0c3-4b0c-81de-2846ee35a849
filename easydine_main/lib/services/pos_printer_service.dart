// lib/services/pos_printer_service.dart

import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bluetooth_serial/flutter_bluetooth_serial.dart';
import 'package:usb_serial/usb_serial.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:ping_discover_network_forked/ping_discover_network.dart';
import 'package:esc_pos_printer/esc_pos_printer.dart';
import 'package:esc_pos_utils/esc_pos_utils.dart';
import '../models/printer_settings.dart';

/// Enhanced POS printer service for discovering and managing printers
class POSPrinterService {
  static final POSPrinterService _instance = POSPrinterService._internal();
  factory POSPrinterService() => _instance;
  POSPrinterService._internal();

  final StreamController<List<PrinterDevice>> _printersController =
  StreamController<List<PrinterDevice>>.broadcast();

  Stream<List<PrinterDevice>> get printersStream => _printersController.stream;

  List<PrinterDevice> _discoveredPrinters = [];
  bool _isDiscovering = false;

  // Active connections
  final Map<String, NetworkPrinter> _networkConnections = {};
  final Map<String, UsbPort> _usbConnections = {};

  /// Initialize the printer service
  Future<void> initialize() async {
    try {
      debugPrint('Initializing printer service...');

      // Initialize Bluetooth
      await FlutterBluetoothSerial.instance.requestEnable();

      // Request USB permissions
      await UsbSerial.listDevices();

    } catch (e) {
      debugPrint('Error initializing printer service: $e');
    }
  }

  /// Discover available printers
  Future<List<PrinterDevice>> discoverPrinters() async {
    if (_isDiscovering) return _discoveredPrinters;

    _isDiscovering = true;
    _discoveredPrinters.clear();

    try {
      debugPrint('Starting printer discovery...');

      // Discover different types of printers concurrently
      await Future.wait([
        _discoverNetworkPrinters(),
        _discoverUSBPrinters(),
        _discoverBluetoothPrinters(),
      ]);

      _printersController.add(_discoveredPrinters);
      debugPrint('Discovery completed. Found ${_discoveredPrinters.length} printers');
      return _discoveredPrinters;
    } catch (e) {
      debugPrint('Error discovering printers: $e');
      return _discoveredPrinters;
    } finally {
      _isDiscovering = false;
    }
  }

  /// Discover USB printers using usb_serial plugin
  Future<void> _discoverUSBPrinters() async {
    try {
      debugPrint('Discovering USB printers...');

      final List<UsbDevice> devices = await UsbSerial.listDevices();

      for (UsbDevice device in devices) {
        // Check if it's a potential printer device
        if (_isPrinterDevice(device)) {
          final printerDevice = PrinterDevice(
            id: 'usb_${device.deviceId}',
            name: '${device.manufacturerName ?? 'Unknown'} ${device.productName ?? 'USB Printer'}',
            type: 'usb',
            isConnected: false,
            deviceId: device.deviceId.toString(),
            vendorId: device.vid,
            productId: device.pid,
          );

          _discoveredPrinters.add(printerDevice);
          debugPrint('Found USB printer: ${printerDevice.name}');
        }
      }
    } catch (e) {
      debugPrint('Error discovering USB printers: $e');
    }
  }

  /// Check if USB device is likely a printer
  bool _isPrinterDevice(UsbDevice device) {
    // Common printer vendor IDs
    final printerVendorIds = [
      0x04B8, // Epson
      0x04E8, // Samsung
      0x03F0, // HP
      0x043D, // Lexmark
      0x04A9, // Canon
      0x0519, // Star Micronics
      0x067B, // Prolific (common in POS printers)
      0x0525, // Netchip (used in some thermal printers)
    ];

    return printerVendorIds.contains(device.vid) ||
        (device.productName?.toLowerCase().contains('printer') ?? false) ||
        (device.manufacturerName?.toLowerCase().contains('star') ?? false) ||
        (device.manufacturerName?.toLowerCase().contains('epson') ?? false);
  }

  /// Discover Bluetooth printers using flutter_bluetooth_serial
  Future<void> _discoverBluetoothPrinters() async {
    try {
      debugPrint('Discovering Bluetooth printers...');

      // Get bonded devices
      final List<BluetoothDevice> bondedDevices =
      await FlutterBluetoothSerial.instance.getBondedDevices();

      for (BluetoothDevice device in bondedDevices) {
        if (_isBluetoothPrinter(device)) {
          final printerDevice = PrinterDevice(
            id: 'bt_${device.address}',
            name: device.name ?? 'Unknown Bluetooth Printer',
            type: 'bluetooth',
            isConnected: device.isConnected,
            deviceId: device.address,
            macAddress: device.address,
          );

          _discoveredPrinters.add(printerDevice);
          debugPrint('Found Bluetooth printer: ${printerDevice.name}');
        }
      }

      // Start discovery for new devices
      if (await FlutterBluetoothSerial.instance.isEnabled ?? false) {
        final StreamSubscription<BluetoothDiscoveryResult> subscription =
        FlutterBluetoothSerial.instance.startDiscovery().listen((result) {
          if (_isBluetoothPrinter(result.device)) {
            final printerDevice = PrinterDevice(
              id: 'bt_${result.device.address}',
              name: result.device.name ?? 'Unknown Bluetooth Printer',
              type: 'bluetooth',
              isConnected: false,
              deviceId: result.device.address,
              macAddress: result.device.address,
            );

            // Avoid duplicates
            if (!_discoveredPrinters.any((p) => p.id == printerDevice.id)) {
              _discoveredPrinters.add(printerDevice);
              _printersController.add(_discoveredPrinters);
              debugPrint('Discovered new Bluetooth printer: ${printerDevice.name}');
            }
          }
        });

        // Stop discovery after 10 seconds
        Timer(const Duration(seconds: 10), () {
          subscription.cancel();
          FlutterBluetoothSerial.instance.cancelDiscovery();
        });
      }

    } catch (e) {
      debugPrint('Error discovering Bluetooth printers: $e');
    }
  }

  /// Check if Bluetooth device is likely a printer
  bool _isBluetoothPrinter(BluetoothDevice device) {
    final name = device.name?.toLowerCase() ?? '';
    return name.contains('printer') ||
        name.contains('pos') ||
        name.contains('receipt') ||
        name.contains('tm-') ||
        name.contains('star') ||
        name.contains('epson') ||
        name.contains('thermal');
  }

  /// Discover Network printers using ping discovery
  Future<void> _discoverNetworkPrinters() async {
    try {
      debugPrint('Discovering network printers...');

      final info = NetworkInfo();
      final wifiIP = await info.getWifiIP();

      if (wifiIP == null) {
        debugPrint('No WiFi connection found');
        return;
      }

      // Extract subnet from IP
      final subnet = wifiIP.substring(0, wifiIP.lastIndexOf('.'));
      debugPrint('Scanning subnet: $subnet.x');

      // Scan common printer ports (9100, 515, 631)
      final commonPorts = [9100, 515, 631];

      // Discover devices on network
      final stream = NetworkAnalyzer.discover2(subnet, 9100);

      await for (NetworkAddress addr in stream) {
        if (addr.exists) {
          // Try to identify if it's a printer by testing common printer ports
          for (int port in commonPorts) {
            try {
              final socket = await Socket.connect(addr.ip, port, timeout: Duration(seconds: 2));
              await socket.close();

              final printerDevice = PrinterDevice(
                id: 'net_${addr.ip}_$port',
                name: 'Network Printer (${addr.ip}:$port)',
                type: 'network',
                isConnected: false,
                deviceId: '${addr.ip}:$port',
                ipAddress: addr.ip,
                port: port,
              );

              _discoveredPrinters.add(printerDevice);
              debugPrint('Found network printer: ${addr.ip}:$port');
              break; // Found one port, move to next IP

            } catch (e) {
              // Port not accessible, continue to next port
            }
          }
        }
      }

    } catch (e) {
      debugPrint('Error discovering network printers: $e');
    }
  }

  /// Connect to printer based on type
  Future<bool> connectToPrinter(PrinterDevice printer) async {
    try {
      debugPrint('Connecting to ${printer.type} printer: ${printer.name}');

      switch (printer.type) {
        case 'network':
          return await _connectNetworkPrinter(printer);
        case 'usb':
          return await _connectUSBPrinter(printer);
        case 'bluetooth':
          return await _connectBluetoothPrinter(printer);
        default:
          debugPrint('Unknown printer type: ${printer.type}');
          return false;
      }
    } catch (e) {
      debugPrint('Error connecting to printer: $e');
      return false;
    }
  }

  /// Connect to network printer
  Future<bool> _connectNetworkPrinter(PrinterDevice printer) async {
    try {
      final parts = printer.deviceId.split(':');
      final ip = parts[0];
      final port = int.parse(parts[1]);

      final profile = await CapabilityProfile.load();
      final networkPrinter = NetworkPrinter(PaperSize.mm80, profile);

      final result = await networkPrinter.connect(ip, port: port);

      if (result == PosPrintResult.success) {
        _networkConnections[printer.id] = networkPrinter;
        _updatePrinterStatus(printer.id, true);
        debugPrint('✅ Connected to network printer: ${printer.name}');
        return true;
      } else {
        debugPrint('❌ Failed to connect to network printer: $result');
        return false;
      }
    } catch (e) {
      debugPrint('Error connecting to network printer: $e');
      return false;
    }
  }

  /// Connect to USB printer
  Future<bool> _connectUSBPrinter(PrinterDevice printer) async {
    try {
      final devices = await UsbSerial.listDevices();
      final device = devices.firstWhere(
            (d) => d.deviceId.toString() == printer.deviceId,
        orElse: () => throw Exception('USB device not found'),
      );

      final port = await UsbSerial.createFromUsbDevice(device);
      final opened = await port.open();

      if (opened) {
        await port.setDTR(true);
        await port.setRTS(true);
        await port.setPortParameters(9600, UsbSerial.DATABITS_8,
            UsbSerial.STOPBITS_1, UsbSerial.PARITY_NONE);

        _usbConnections[printer.id] = port;
        _updatePrinterStatus(printer.id, true);
        debugPrint('✅ Connected to USB printer: ${printer.name}');
        return true;
      } else {
        debugPrint('❌ Failed to open USB port');
        return false;
      }
    } catch (e) {
      debugPrint('Error connecting to USB printer: $e');
      return false;
    }
  }

  /// Connect to Bluetooth printer
  Future<bool> _connectBluetoothPrinter(PrinterDevice printer) async {
    try {
      // Implementation would depend on specific Bluetooth printer protocol
      // This is a simplified version
      debugPrint('Bluetooth printer connection not fully implemented');
      return false;
    } catch (e) {
      debugPrint('Error connecting to Bluetooth printer: $e');
      return false;
    }
  }

  /// Update printer connection status
  void _updatePrinterStatus(String printerId, bool isConnected) {
    final index = _discoveredPrinters.indexWhere((p) => p.id == printerId);
    if (index != -1) {
      _discoveredPrinters[index] = _discoveredPrinters[index].copyWith(isConnected: isConnected);
      _printersController.add(_discoveredPrinters);
    }
  }

  /// Disconnect from printer
  Future<bool> disconnectPrinter(PrinterDevice printer) async {
    try {
      switch (printer.type) {
        case 'network':
          final networkPrinter = _networkConnections[printer.id];
          if (networkPrinter != null) {
            networkPrinter.disconnect();
            _networkConnections.remove(printer.id);
          }
          break;
        case 'usb':
          final usbPort = _usbConnections[printer.id];
          if (usbPort != null) {
            await usbPort.close();
            _usbConnections.remove(printer.id);
          }
          break;
        case 'bluetooth':
        // Implement Bluetooth disconnection
          break;
      }

      _updatePrinterStatus(printer.id, false);
      debugPrint('🔌 Disconnected from printer: ${printer.name}');
      return true;
    } catch (e) {
      debugPrint('Error disconnecting from printer: $e');
      return false;
    }
  }

  /// Print test page
  Future<bool> printTestPage(PrinterDevice printer) async {
    try {
      if (!printer.isConnected) {
        final connected = await connectToPrinter(printer);
        if (!connected) return false;
      }

      switch (printer.type) {
        case 'network':
          return await _printNetworkTestPage(printer);
        case 'usb':
          return await _printUSBTestPage(printer);
        case 'bluetooth':
          return await _printBluetoothTestPage(printer);
        default:
          return false;
      }
    } catch (e) {
      debugPrint('Error printing test page: $e');
      return false;
    }
  }

  /// Print test page on network printer
  Future<bool> _printNetworkTestPage(PrinterDevice printer) async {
    try {
      final networkPrinter = _networkConnections[printer.id];
      if (networkPrinter == null) return false;

      networkPrinter.text('TEST PAGE\n',
          styles: const PosStyles(
            align: PosAlign.center,
            height: PosTextSize.size2,
            width: PosTextSize.size2,
          ));

      networkPrinter.text('Printer: ${printer.name}\n');
      networkPrinter.text('Type: ${printer.type.toUpperCase()}\n');
      networkPrinter.text('Time: ${DateTime.now()}\n');
      networkPrinter.text('Status: Connected ✓\n');
      networkPrinter.feed(2);
      networkPrinter.cut();

      debugPrint('🖨️ Test page printed on: ${printer.name}');
      return true;
    } catch (e) {
      debugPrint('Error printing network test page: $e');
      return false;
    }
  }

  /// Print test page on USB printer
  Future<bool> _printUSBTestPage(PrinterDevice printer) async {
    try {
      final usbPort = _usbConnections[printer.id];
      if (usbPort == null) return false;

      // Basic ESC/POS commands for test page
      final testData = [
        0x1B, 0x40, // Initialize printer
        ...('TEST PAGE\n').codeUnits,
        ...('Printer: ${printer.name}\n').codeUnits,
        ...('Type: ${printer.type.toUpperCase()}\n').codeUnits,
        ...('Time: ${DateTime.now()}\n').codeUnits,
        ...('Status: Connected ✓\n').codeUnits,
        0x0A, 0x0A, // Line feeds
        0x1D, 0x56, 0x00, // Cut paper
      ];

      await usbPort.write(Uint8List.fromList(testData));

      debugPrint('🖨️ Test page printed on: ${printer.name}');
      return true;
    } catch (e) {
      debugPrint('Error printing USB test page: $e');
      return false;
    }
  }

  /// Print test page on Bluetooth printer
  Future<bool> _printBluetoothTestPage(PrinterDevice printer) async {
    try {
      // Implement Bluetooth printing
      debugPrint('Bluetooth printing not fully implemented');
      return false;
    } catch (e) {
      debugPrint('Error printing Bluetooth test page: $e');
      return false;
    }
  }

  /// Dispose resources
  void dispose() {
    // Close all connections
    for (final connection in _networkConnections.values) {
      connection.disconnect();
    }
    _networkConnections.clear();

    for (final connection in _usbConnections.values) {
      connection.close();
    }
    _usbConnections.clear();

    _printersController.close();
  }
}