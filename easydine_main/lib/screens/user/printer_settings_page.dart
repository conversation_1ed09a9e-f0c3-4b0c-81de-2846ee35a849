// lib/screens/user/printer_settings_page.dart

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';
import '../../blocs/printer_settings/printer_settings_bloc.dart';
import '../../models/printer_settings.dart';

class PrinterSettingsPage extends StatelessWidget {
  const PrinterSettingsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => PrinterSettingsBloc()..add(LoadPrinterSettings()),
      child: const PrinterSettingsView(),
    );
  }
}

class PrinterSettingsView extends StatelessWidget {
  const PrinterSettingsView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Scaffold(
      backgroundColor: const Color(0xFF0A0E1A),
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.print,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Text(
              'Printer Management',
              style: GoogleFonts.inter(
                fontWeight: FontWeight.w700,
                fontSize: isTablet ? 24 : 20,
                color: Colors.white,
                letterSpacing: -0.5,
              ),
            ),
          ],
        ),
        backgroundColor: const Color(0xFF1E293B),
        foregroundColor: Colors.white,
        elevation: 0,
        toolbarHeight: isTablet ? 80 : 56,
        actions: [
          BlocBuilder<PrinterSettingsBloc, PrinterSettingsState>(
            builder: (context, state) {
              return Container(
                margin: const EdgeInsets.only(right: 8),
                child: ElevatedButton.icon(
                  onPressed: state.isDiscovering
                      ? null
                      : () {
                          context
                              .read<PrinterSettingsBloc>()
                              .add(DiscoverPrinters());
                        },
                  icon: state.isDiscovering
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Icon(Icons.search, size: 18),
                  label: Text(
                    state.isDiscovering ? 'Scanning...' : 'Discover',
                    style: GoogleFonts.inter(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF6366F1),
                    foregroundColor: Colors.white,
                    elevation: 0,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              );
            },
          ),
          Container(
            margin: const EdgeInsets.only(right: 16),
            child: IconButton(
              icon: const Icon(Icons.refresh_rounded, size: 24),
              onPressed: () {
                context.read<PrinterSettingsBloc>().add(LoadPrinterSettings());
              },
              tooltip: 'Refresh',
              style: IconButton.styleFrom(
                backgroundColor: const Color(0xFF374151),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
      body: BlocConsumer<PrinterSettingsBloc, PrinterSettingsState>(
        listener: (context, state) {
          if (state.error != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.error!),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state.isLoading) {
            return const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            );
          }

          return SingleChildScrollView(
            padding: EdgeInsets.all(4.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionHeader('Customized Printing'),
                SizedBox(height: 3.h),
                _buildAssignPrintersSection(context, state),
                SizedBox(height: 4.h),
                _buildAssignCategoriesSection(context, state),
                SizedBox(height: 4.h),
                _buildActionButtons(context),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: GoogleFonts.poppins(
        fontSize: 6.sp,
        fontWeight: FontWeight.w700,
        color: Colors.white,
      ),
    );
  }

  Widget _buildAssignPrintersSection(
      BuildContext context, PrinterSettingsState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Assign Printers',
          style: GoogleFonts.poppins(
            fontSize: 4.5.sp,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        SizedBox(height: 2.h),
        _buildPrinterGrid(context, state),
      ],
    );
  }

  Widget _buildPrinterGrid(BuildContext context, PrinterSettingsState state) {
    final printers = state.settings.availablePrinters;

    if (printers.isEmpty) {
      return Container(
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color: Colors.grey[800],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[600]!),
        ),
        child: Column(
          children: [
            Icon(
              Icons.print_disabled,
              size: 12.w,
              color: Colors.grey[400],
            ),
            SizedBox(height: 2.h),
            Text(
              'No printers found',
              style: GoogleFonts.poppins(
                fontSize: 4.sp,
                color: Colors.grey[400],
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              'Tap the search icon to discover printers',
              style: GoogleFonts.poppins(
                fontSize: 3.sp,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: printers.length,
      itemBuilder: (context, index) {
        final printer = printers[index];
        return _buildPrinterCard(context, state, printer, index);
      },
    );
  }

  Widget _buildPrinterCard(BuildContext context, PrinterSettingsState state,
      PrinterDevice printer, int index) {
    final locations = PrinterLocation.getAllLocations();
    final currentLocation =
        state.settings.printerAssignments[printer.id] ?? 'Not assigned';

    return Container(
      margin: EdgeInsets.only(bottom: 2.h),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: printer.isConnected ? Colors.green : Colors.grey[600]!,
          width: printer.isConnected ? 2 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getPrinterIcon(printer.type),
                color: printer.isConnected ? Colors.green : Colors.grey[400],
                size: 6.w,
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      printer.name,
                      style: GoogleFonts.poppins(
                        fontSize: 4.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      '${printer.type.toUpperCase()} • ${printer.isConnected ? 'Connected' : 'Disconnected'}',
                      style: GoogleFonts.poppins(
                        fontSize: 3.sp,
                        color: printer.isConnected
                            ? Colors.green
                            : Colors.grey[400],
                      ),
                    ),
                  ],
                ),
              ),
              _buildConnectionButton(context, printer),
            ],
          ),
          if (printer.ipAddress != null || printer.macAddress != null) ...[
            SizedBox(height: 2.h),
            Text(
              _getPrinterDetails(printer),
              style: GoogleFonts.poppins(
                fontSize: 2.5.sp,
                color: Colors.grey[400],
              ),
            ),
          ],
          SizedBox(height: 2.h),
          Row(
            children: [
              Expanded(
                child: Text(
                  'Assign to:',
                  style: GoogleFonts.poppins(
                    fontSize: 3.5.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
                  decoration: BoxDecoration(
                    color: Colors.grey[700],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[600]!),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      value: currentLocation,
                      dropdownColor: Colors.grey[800],
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: 3.sp,
                      ),
                      icon: const Icon(Icons.arrow_drop_down,
                          color: Colors.white),
                      items: ['Not assigned', ...locations]
                          .map((location) => DropdownMenuItem(
                                value: location,
                                child: Text(location),
                              ))
                          .toList(),
                      onChanged: (value) {
                        if (value != null) {
                          context.read<PrinterSettingsBloc>().add(
                                UpdatePrinterAssignment(
                                  printerId: printer.id,
                                  location: value,
                                ),
                              );
                        }
                      },
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: printer.isConnected
                      ? () {
                          context.read<PrinterSettingsBloc>().add(
                                TestPrinter(printerId: printer.id),
                              );
                        }
                      : null,
                  icon: Icon(Icons.print, size: 4.w),
                  label: Text(
                    'Test Print',
                    style: GoogleFonts.poppins(fontSize: 3.sp),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 1.5.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              SizedBox(width: 2.w),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    context.read<PrinterSettingsBloc>().add(
                          RemovePrinterDevice(printerId: printer.id),
                        );
                  },
                  icon: Icon(Icons.delete, size: 4.w),
                  label: Text(
                    'Remove',
                    style: GoogleFonts.poppins(fontSize: 3.sp),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 1.5.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildConnectionButton(BuildContext context, PrinterDevice printer) {
    return ElevatedButton(
      onPressed: () {
        if (printer.isConnected) {
          context.read<PrinterSettingsBloc>().add(
                DisconnectPrinter(printerId: printer.id),
              );
        } else {
          context.read<PrinterSettingsBloc>().add(
                ConnectToPrinter(printerId: printer.id),
              );
        }
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: printer.isConnected ? Colors.red : Colors.green,
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: Text(
        printer.isConnected ? 'Disconnect' : 'Connect',
        style: GoogleFonts.poppins(fontSize: 2.5.sp),
      ),
    );
  }

  IconData _getPrinterIcon(String type) {
    switch (type.toLowerCase()) {
      case 'usb':
        return Icons.usb;
      case 'bluetooth':
        return Icons.bluetooth;
      case 'network':
        return Icons.wifi;
      case 'thermal':
        return Icons.receipt;
      default:
        return Icons.print;
    }
  }

  String _getPrinterDetails(PrinterDevice printer) {
    if (printer.ipAddress != null) {
      return 'IP: ${printer.ipAddress}:${printer.port ?? 9100}';
    } else if (printer.macAddress != null) {
      return 'MAC: ${printer.macAddress}';
    } else if (printer.deviceId != null) {
      return 'Device ID: ${printer.deviceId}';
    }
    return '';
  }

  Widget _buildAssignCategoriesSection(
      BuildContext context, PrinterSettingsState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Assign Categories',
          style: GoogleFonts.poppins(
            fontSize: 4.5.sp,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        SizedBox(height: 2.h),
        _buildCategoriesGrid(context, state),
      ],
    );
  }

  Widget _buildCategoriesGrid(
      BuildContext context, PrinterSettingsState state) {
    final assignedLocations = state.settings.getAssignedLocations();
    if (assignedLocations.isEmpty) {
      return Container(
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color: Colors.grey[800],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[600]!),
        ),
        child: Text(
          'No printers assigned to locations yet. Please assign printers above first.',
          style: GoogleFonts.poppins(
            color: Colors.white70,
            fontSize: 3.5.sp,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }

    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[600]!),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: assignedLocations.map((location) {
          return Expanded(
            child: _buildLocationColumn(context, state, location),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildLocationColumn(
      BuildContext context, PrinterSettingsState state, String location) {
    final categoriesForLocation =
        state.settings.getCategoriesForLocation(location);
    final availableCategories = state.availableCategories.isNotEmpty
        ? state.availableCategories
        : FoodCategory.getAllCategories();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          location,
          style: GoogleFonts.poppins(
            fontSize: 4.sp,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        SizedBox(height: 2.h),
        ...availableCategories.map((category) {
          final isAssigned = categoriesForLocation.contains(category);
          return Padding(
            padding: EdgeInsets.only(bottom: 1.h),
            child: Row(
              children: [
                Checkbox(
                  value: isAssigned,
                  onChanged: (value) {
                    context.read<PrinterSettingsBloc>().add(
                          UpdateCategoryAssignment(
                            category: category,
                            location: value == true ? location : '',
                          ),
                        );
                  },
                  activeColor: Colors.blue,
                  checkColor: Colors.white,
                ),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    category,
                    style: GoogleFonts.poppins(
                      fontSize: 3.sp,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: () {
              _showResetConfirmationDialog(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: 2.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Reset to Default',
              style: GoogleFonts.poppins(
                fontSize: 3.5.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        SizedBox(width: 4.w),
        Expanded(
          child: ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: 2.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Save & Close',
              style: GoogleFonts.poppins(
                fontSize: 3.5.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _showResetConfirmationDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          backgroundColor: Colors.grey[800],
          title: Text(
            'Reset Printer Settings',
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: Text(
            'Are you sure you want to reset all printer settings to default? This action cannot be undone.',
            style: GoogleFonts.poppins(
              color: Colors.white70,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: Text(
                'Cancel',
                style: GoogleFonts.poppins(color: Colors.white70),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                context.read<PrinterSettingsBloc>().add(ResetPrinterSettings());
              },
              child: Text(
                'Reset',
                style: GoogleFonts.poppins(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }
}
